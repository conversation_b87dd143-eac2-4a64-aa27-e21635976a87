/**
 * Utilities for generating MDX formatted content from Rule objects
 */

/**
 * Escape a string value for safe inclusion in YAML frontmatter
 * Handles quotes, newlines, and other special characters
 */
function escapeYamlValue(value: string): string {
  if (!value) return '';

  // If the value contains special characters, quotes, or starts/ends with whitespace,
  // we need to quote it and escape internal quotes
  const needsQuoting = /[:"'`\n\r\t\[\]{}|>@#%&*!]|^\s|\s$/.test(value);

  if (needsQuoting) {
    // Escape double quotes and wrap in double quotes
    return `"${value.replace(/"/g, '\\"').replace(/\n/g, '\\n').replace(/\r/g, '\\r')}"`;
  }

  return value;
}

/**
 * Generate MDX content from a rule object
 * 
 * @param rule - The rule object to convert
 * @param options - Configuration options for the output
 * @returns MDX formatted string with frontmatter and content
 */
export function generateMDXContent(
  rule: any,
  options: {
    includeId?: boolean;
    useISODates?: boolean;
  } = {}
): string {
  const { includeId = false, useISODates = false } = options;
  
  const tags = rule.tags.map((t: any) => t.tag.name).join(', ');
  const author = rule.user?.name || 'Unknown';
  
  // Choose date format based on options
  const createdDate = useISODates 
    ? new Date(rule.createdAt).toISOString()
    : new Date(rule.createdAt).toLocaleDateString();
  const updatedDate = useISODates
    ? new Date(rule.updatedAt).toISOString() 
    : new Date(rule.updatedAt).toLocaleDateString();
  
  // Build frontmatter
  let frontmatter = '';
  if (includeId) {
    frontmatter += `id: ${escapeYamlValue(rule.id)}\n`;
  }
  frontmatter += `title: ${escapeYamlValue(rule.title)}\n`;
  frontmatter += `description: ${escapeYamlValue(rule.description || '')}\n`;
  frontmatter += `author: ${escapeYamlValue(author)}\n`;
  frontmatter += `createdAt: ${escapeYamlValue(createdDate)}\n`;
  frontmatter += `updatedAt: ${escapeYamlValue(updatedDate)}\n`;
  frontmatter += `ideType: ${escapeYamlValue(rule.ideType)}\n`;
  frontmatter += `visibility: ${escapeYamlValue(rule.visibility)}\n`;
  frontmatter += `applyType: ${escapeYamlValue(rule.applyType || 'manual')}\n`;
  if (rule.glob) {
    frontmatter += `glob: ${escapeYamlValue(rule.glob)}\n`;
  }
  frontmatter += `tags: [${rule.tags.map((t: any) => `"${t.tag.name.replace(/"/g, '\\"')}"`).join(', ')}]`;
  
  // Generate MDX content
  const mdx = `---
${frontmatter}
---

# ${rule.title}

${rule.description ? `> ${rule.description}\n\n` : ''}


## Rule Content

${rule.content}
`;

  return mdx;
}
